import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Icon,
  Textarea,
  Typography,
  Card,
} from '@/shared/components/common';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { ProductStatus, ProductCategory } from '../../types/product-for-sale.types';

// Định nghĩa type cho form values
export interface ProductForSaleFormValues {
  name: string;
  description?: string;
  image?: string;
  price?: number;
  category: ProductCategory;
  detail?: string;
  userManual?: string;
}

/**
 * Component con để handle textarea với form context
 */
interface FormTextareaProps {
  name: string;
  label: string;
  placeholder: string;
  originalValue: string;
  onChangeDetected: (hasChanged: boolean) => void;
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  name,
  label,
  placeholder,
  originalValue,
  onChangeDetected
}) => {
  const { setValue } = useFormContext<ProductForSaleFormValues>();
  const [displayValue, setDisplayValue] = useState<string>(''); // Giá trị hiển thị trong textarea
  const [originalContent, setOriginalContent] = useState<string>(''); // Nội dung gốc để so sánh
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [hasUserChanges, setHasUserChanges] = useState<boolean>(false); // Track user changes
  const { t } = useTranslation(['marketplace', 'common']);

  // Reset khi originalValue thay đổi (khi product thay đổi)
  useEffect(() => {
    console.log(`🔄 [${name}] originalValue changed, resetting initialization...`);
    setIsInitialized(false);
    setDisplayValue('');
    setOriginalContent('');
    setIsLoading(false);
    setHasUserChanges(false); // Reset user changes flag
  }, [originalValue, name]);

  // Fetch nội dung từ CDN URL chỉ một lần khi component mount
  useEffect(() => {
    if (isInitialized) return;

    const fetchContentFromUrl = async (url: string) => {
      if (!url) {
        console.log(`❌ [${name}] Empty URL provided`);
        return '';
      }

      console.log(`🔍 [${name}] ==================== FETCHING ====================`);
      console.log(`🔍 [${name}] Input URL:`, url);

      // Làm sạch URL - giữ nguyên query parameters
      const cleanUrl = url.trim();
      console.log(`🔍 [${name}] Clean URL:`, cleanUrl);

      try {
        setIsLoading(true);
        console.log(`🔍 [${name}] Starting fetch request...`);

        // Thêm timeout 10 giây
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(cleanUrl, {
          signal: controller.signal,
          method: 'GET',
          headers: {
            'Accept': 'text/plain, text/html, */*',
          },
          mode: 'cors', // Explicitly set CORS mode
          cache: 'no-cache', // Avoid cache issues
        });

        clearTimeout(timeoutId);

        console.log(`🔍 [${name}] Response status:`, response.status);
        console.log(`🔍 [${name}] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          const content = await response.text();
          console.log(`✅ [${name}] ✅ FETCH SUCCESS!`);
          console.log(`✅ [${name}] Content length:`, content.length);
          console.log(`✅ [${name}] Content preview:`, content.substring(0, 200) + '...');
          return content;
        } else {
          console.error(`❌ [${name}] ❌ HTTP ERROR:`, response.status, response.statusText);
          const errorText = await response.text();
          console.error(`❌ [${name}] Error response:`, errorText.substring(0, 200));
          return '';
        }
      } catch (error) {
        console.error(`❌ [${name}] ❌ FETCH EXCEPTION:`, error);
        if (error instanceof Error) {
          console.error(`❌ [${name}] Error name:`, error.name);
          console.error(`❌ [${name}] Error message:`, error.message);

          // Specific handling for common errors
          if (error.name === 'AbortError') {
            console.error(`❌ [${name}] Request timed out after 10 seconds`);
          } else if (error.message.includes('CORS')) {
            console.error(`❌ [${name}] CORS error - URL may not allow cross-origin requests`);
          } else if (error.message.includes('Failed to fetch')) {
            console.error(`❌ [${name}] Network error - URL may be unreachable`);
          }
        }
        return '';
      } finally {
        setIsLoading(false);
        console.log(`🔍 [${name}] ==================== FETCH END ====================`);
      }
    };

    const initializeContent = async () => {
      let content = '';

      console.log(`🔍 [${name}] ==================== INITIALIZING ====================`);
      console.log(`🔍 [${name}] originalValue:`, originalValue);
      console.log(`🔍 [${name}] originalValue type:`, typeof originalValue);
      console.log(`🔍 [${name}] originalValue length:`, originalValue?.length);
      console.log(`🔍 [${name}] Is URL?:`, originalValue?.startsWith?.('https://cdn.redai.vn/'));
      console.log(`🔍 [${name}] Contains cdn.redai.vn?:`, originalValue?.includes?.('cdn.redai.vn'));

      // Kiểm tra nếu originalValue là URL CDN - LOGIC MẠNH HƠN
      const isUrl = originalValue && originalValue.length > 10 && (
        originalValue.startsWith('https://cdn.redai.vn/') ||
        originalValue.includes('cdn.redai.vn') ||
        (originalValue.startsWith('https://') && originalValue.includes('expires=')) // Presigned URL
      );

      console.log(`🔍 [${name}] isUrl decision:`, isUrl);

      if (isUrl) {
        console.log(`🔍 [${name}] ✅ DETECTED URL - FETCHING CONTENT...`);
        content = await fetchContentFromUrl(originalValue);

        // Nếu fetch thất bại, hiển thị thông báo thay vì URL
        if (!content || content.trim() === '') {
          console.log(`⚠️ [${name}] ❌ FETCH FAILED - setting placeholder message instead of showing URL`);
          content = `[Không thể tải nội dung từ: ${originalValue}]\n\nVui lòng nhập nội dung mới hoặc liên hệ admin để kiểm tra đường dẫn.`;
        } else {
          console.log(`✅ [${name}] ✅ FETCH SUCCESS - content length:`, content.length);
        }
      } else {
        // Nếu không phải URL, sử dụng trực tiếp
        console.log(`🔍 [${name}] ❌ NOT URL - using originalValue directly`);
        content = originalValue || '';
      }

      console.log(`✅ [${name}] Final content:`, content.substring(0, 100) + '...');

      setOriginalContent(content);
      setDisplayValue(content);
      setValue(name as keyof ProductForSaleFormValues, content as string);

      // Reset change detection khi khởi tạo - chỉ reset nếu chưa có user changes
      if (!hasUserChanges) {
        console.log(`🔄 [${name}] RESET change detection to FALSE on initialization`);
        onChangeDetected(false);
      } else {
        console.log(`🔄 [${name}] SKIP reset - user has made changes`);
      }

      setIsInitialized(true);
    };

    initializeContent();
  }, [originalValue, name, setValue, onChangeDetected, isInitialized, hasUserChanges]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue); // Cập nhật giá trị hiển thị
    setValue(name as keyof ProductForSaleFormValues, newValue as string); // Cập nhật form state
    setHasUserChanges(true); // Mark that user has made changes

    // Check if changed from original content (not URL)
    const hasChanged = newValue.trim() !== originalContent.trim();
    console.log(`📝 [${name}] CHANGE DETECTION:`, {
      hasChanged,
      originalLength: originalContent.length,
      newLength: newValue.length,
      originalPreview: originalContent.substring(0, 50) + '...',
      newPreview: newValue.substring(0, 50) + '...',
      originalTrimmed: originalContent.trim().substring(0, 30),
      newTrimmed: newValue.trim().substring(0, 30),
      hasUserChanges: true
    });

    onChangeDetected(hasChanged);
  };

  return (
    <FormItem name={name} label={label}>
      <Textarea
        rows={4}
        placeholder={isLoading ? t('common:loading', 'Đang tải nội dung...') : placeholder}
        fullWidth
        value={displayValue} // Sử dụng displayValue thay vì currentValue từ form
        onChange={handleChange}
        disabled={isLoading}
      />
    </FormItem>
  );
};

// Interface cho image object
interface ImageObject {
  url: string;
  key: string;
  position?: number;
}

interface ProductForSaleFormProps {
  onSubmit: (
    values: ProductForSaleFormValues & {
      imageFiles?: File[]; // Thay đổi để hỗ trợ nhiều ảnh
      oldImageKeys?: string[]; // Keys của ảnh cũ (để reference)
      deletedImageKeys?: string[]; // Keys của ảnh bị xóa (để gửi DELETE operations)
      hasImageChanged?: boolean; // Flag để biết ảnh có thay đổi không
      uploadedImageUrls?: string[]; // URLs của ảnh đã upload thành công
      hasDetailChanged?: boolean; // Flag để biết detail có thay đổi không
      hasUserManualChanged?: boolean; // Flag để biết userManual có thay đổi không
    },
    submitForApproval?: boolean
  ) => void;
  onCancel: () => void;
  initialValues?: Partial<
    ProductForSaleFormValues & {
      oldImageKeys?: string[];
      images?: (string | ImageObject)[]; // Hỗ trợ cả string và object
      detail?: string;
      userManual?: string;
    }
  >;
  isSubmitting?: boolean;
}

/**
 * Form thêm/sửa sản phẩm đăng bán
 */
const ProductForSaleForm: React.FC<ProductForSaleFormProps> = ({
  onSubmit,
  onCancel,
  initialValues,
  isSubmitting = false,
}) => {
  const { t } = useTranslation('marketplace');

  // Tạo schema với translation

  // State cho image upload - hỗ trợ nhiều ảnh
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [hasImageChanged, setHasImageChanged] = useState<boolean>(false);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [deletedImageKeys, setDeletedImageKeys] = useState<string[]>([]); // Track ảnh bị xóa

  // Track mapping giữa preview và key để tránh lỗi index
  const [imageKeyMapping, setImageKeyMapping] = useState<Map<string, string>>(new Map());

  // Track thay đổi detail và userManual
  const [hasDetailChanged, setHasDetailChanged] = useState<boolean>(false);
  const [hasUserManualChanged, setHasUserManualChanged] = useState<boolean>(false);
  const originalDetail = initialValues?.detail || '';
  const originalUserManual = initialValues?.userManual || '';

  // Debug state changes
  useEffect(() => {
    console.log(`🔍 [MAIN] hasDetailChanged changed to:`, hasDetailChanged);
  }, [hasDetailChanged]);

  useEffect(() => {
    console.log(`🔍 [MAIN] hasUserManualChanged changed to:`, hasUserManualChanged);
  }, [hasUserManualChanged]);

  // Khởi tạo mapping và previews khi component mount
  useEffect(() => {
    if (initialValues?.images && Array.isArray(initialValues.images)) {
      const mapping = new Map<string, string>();
      const previews: string[] = [];

      initialValues.images.forEach((imageObj, index) => {
        // Xử lý cấu trúc mới: { key, position, url }
        if (typeof imageObj === 'object' && imageObj !== null && 'url' in imageObj && 'key' in imageObj) {
          const imgObj = imageObj as ImageObject;
          mapping.set(imgObj.url, imgObj.key);
          previews.push(imgObj.url);
          console.log(`🔍 [INIT] Mapped image ${index}:`, { url: imgObj.url, key: imgObj.key });
        } else if (typeof imageObj === 'string') {
          // Fallback cho cấu trúc cũ (array of strings)
          const key = initialValues.oldImageKeys?.[index] || `image-${index}-${imageObj.split('/').pop()}`;
          mapping.set(imageObj, key);
          previews.push(imageObj);
          console.log(`🔍 [INIT] Mapped legacy image ${index}:`, { url: imageObj, key });
        }
      });

      setImageKeyMapping(mapping);
      setImagePreviews(previews);
      console.log('🔍 [INIT] Final image key mapping:', mapping);
      console.log('🔍 [INIT] Final image previews:', previews);
    }
  }, [initialValues?.images, initialValues?.oldImageKeys]);

  // Xử lý khi thêm ảnh mới
  const handleAddImage = (file: File, dataUrl: string) => {
    console.log('🔍 [ADD IMAGE] Adding new image:', { fileName: file.name, fileSize: file.size, fileType: file.type });
    setImageFiles(prev => {
      const newFiles = [...prev, file];
      console.log('🔍 [ADD IMAGE] Updated imageFiles count:', newFiles.length);
      return newFiles;
    });
    setImagePreviews(prev => {
      const newPreviews = [...prev, dataUrl];
      console.log('🔍 [ADD IMAGE] Updated imagePreviews count:', newPreviews.length);
      return newPreviews;
    });
    setHasImageChanged(true);
  };

  // Xử lý khi xóa ảnh
  const handleRemoveImage = (index: number) => {
    console.log('🔍 [REMOVE IMAGE] Starting removal for index:', index);
    console.log('🔍 [REMOVE IMAGE] Current imagePreviews:', imagePreviews);
    console.log('🔍 [REMOVE IMAGE] Current imageFiles:', imageFiles);
    console.log('🔍 [REMOVE IMAGE] Current imageKeyMapping:', imageKeyMapping);

    // Lấy URL của ảnh đang xóa
    const imageUrl = imagePreviews[index];
    console.log('🔍 [REMOVE IMAGE] Image URL to remove:', imageUrl);

    // Kiểm tra xem có phải ảnh cũ không bằng cách tìm trong mapping
    const correspondingKey = imageKeyMapping.get(imageUrl);
    if (correspondingKey) {
      console.log('🔍 [REMOVE IMAGE] Removing OLD image with key:', correspondingKey);
      setDeletedImageKeys(prev => {
        const newDeleted = [...prev, correspondingKey];
        console.log('🗑️ [REMOVE IMAGE] Updated deletedImageKeys:', newDeleted);
        return newDeleted;
      });
      // Xóa khỏi mapping
      setImageKeyMapping(prev => {
        const newMapping = new Map(prev);
        newMapping.delete(imageUrl);
        console.log('🔍 [REMOVE IMAGE] Updated imageKeyMapping:', newMapping);
        return newMapping;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] Removing NEW image (no key found)');
    }

    // Xác định số ảnh gốc từ initialValues
    const originalImageCount = (initialValues?.images?.length || 0);
    console.log('🔍 [REMOVE IMAGE] Original image count:', originalImageCount);
    console.log('🔍 [REMOVE IMAGE] Removing index:', index);

    if (index >= originalImageCount) {
      // Đây là ảnh mới, xóa khỏi imageFiles
      const newImageIndex = index - originalImageCount;
      console.log('🔍 [REMOVE IMAGE] This is a NEW image, removing from imageFiles at index:', newImageIndex);
      setImageFiles(prev => {
        const newFiles = prev.filter((_, i) => i !== newImageIndex);
        console.log('🔍 [REMOVE IMAGE] Updated imageFiles:', newFiles);
        return newFiles;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] This is an ORIGINAL image, not removing from imageFiles');
    }

    // Luôn xóa khỏi imagePreviews
    setImagePreviews(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imagePreviews:', newPreviews);
      return newPreviews;
    });

    setUploadedImageUrls(prev => prev.filter((_, i) => i !== index));
    setHasImageChanged(true);
  };

  // Xử lý khi submit form (lưu draft)
  const handleFormSubmit: SubmitHandler<ProductForSaleFormValues> = values => {
    // Validation: Kiểm tra có ảnh không
    if (imageFiles.length === 0 && imagePreviews.length === 0) {
      alert(
        t('productsForSale.form.validation.imageRequired', 'Vui lòng chọn ít nhất một ảnh sản phẩm')
      );
      return;
    }

    console.log('🔍 [ProductForSaleForm] Form submitted with values:', values);
    console.log('🔍 [ProductForSaleForm] Image files:', imageFiles);
    console.log('🔍 [ProductForSaleForm] Has detail changed:', hasDetailChanged);
    console.log('🔍 [ProductForSaleForm] Has userManual changed:', hasUserManualChanged);
    console.log('🔍 [ProductForSaleForm] Is editing:', !!initialValues?.name);

    // Đối với tạo mới, luôn coi như có thay đổi nếu có nội dung
    const isCreating = !initialValues?.name;
    const finalHasDetailChanged = isCreating
      ? !!(values.detail && values.detail.trim())
      : hasDetailChanged;
    const finalHasUserManualChanged = isCreating
      ? !!(values.userManual && values.userManual.trim())
      : hasUserManualChanged;

    console.log('🔍 [ProductForSaleForm] Final hasDetailChanged:', finalHasDetailChanged);
    console.log('🔍 [ProductForSaleForm] Final hasUserManualChanged:', finalHasUserManualChanged);

    onSubmit(
      {
        ...values,
        imageFiles, // Danh sách file ảnh mới
        oldImageKeys: initialValues?.oldImageKeys, // Keys của ảnh cũ (để reference)
        deletedImageKeys, // Keys của ảnh bị xóa (để gửi DELETE operations)
        hasImageChanged, // Flag để biết ảnh có thay đổi không
        uploadedImageUrls, // URLs của ảnh đã upload
        hasDetailChanged: finalHasDetailChanged, // Flag để biết detail có thay đổi không
        hasUserManualChanged: finalHasUserManualChanged, // Flag để biết userManual có thay đổi không
        status: ProductStatus.ACTIVE,
      } as ProductForSaleFormValues & {
        imageFiles?: File[];
        oldImageKeys?: string[];
        deletedImageKeys?: string[];
        hasImageChanged?: boolean;
        uploadedImageUrls?: string[];
        hasDetailChanged?: boolean;
        hasUserManualChanged?: boolean;
      },
      false
    );
  };

  // Xử lý khi submit form (gửi duyệt)
  const handleSubmitForApproval: SubmitHandler<ProductForSaleFormValues> = values => {
    // Validation: Kiểm tra có ảnh không
    if (imageFiles.length === 0 && imagePreviews.length === 0) {
      alert(
        t('productsForSale.form.validation.imageRequired', 'Vui lòng chọn ít nhất một ảnh sản phẩm')
      );
      return;
    }

    // Đối với tạo mới, luôn coi như có thay đổi nếu có nội dung
    const isCreating = !initialValues?.name;
    const finalHasDetailChanged = isCreating
      ? !!(values.detail && values.detail.trim())
      : hasDetailChanged;
    const finalHasUserManualChanged = isCreating
      ? !!(values.userManual && values.userManual.trim())
      : hasUserManualChanged;

    onSubmit(
      {
        ...values,
        imageFiles, // Danh sách file ảnh mới
        oldImageKeys: initialValues?.oldImageKeys, // Keys của ảnh cũ (để reference)
        deletedImageKeys, // Keys của ảnh bị xóa (để gửi DELETE operations)
        hasImageChanged, // Flag để biết ảnh có thay đổi không
        uploadedImageUrls, // URLs của ảnh đã upload
        hasDetailChanged: finalHasDetailChanged, // Flag để biết detail có thay đổi không
        hasUserManualChanged: finalHasUserManualChanged, // Flag để biết userManual có thay đổi không
        status: ProductStatus.ACTIVE,
      } as ProductForSaleFormValues & {
        imageFiles?: File[];
        oldImageKeys?: string[];
        deletedImageKeys?: string[];
        hasImageChanged?: boolean;
        uploadedImageUrls?: string[];
        hasDetailChanged?: boolean;
        hasUserManualChanged?: boolean;
      },
      true
    );
  };

  // Giả lập URL ảnh ngẫu nhiên
  const getRandomImageUrl = () => {
    return `/images/products/product-${Math.floor(Math.random() * 10) + 1}.jpg`;
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h4">
          {initialValues?.name
            ? t('productsForSale.editProduct', 'Chỉnh sửa sản phẩm')
            : t('productsForSale.addProduct', 'Thêm sản phẩm mới')}
        </Typography>

        <Form
          onSubmit={handleFormSubmit as unknown as SubmitHandler<Record<string, unknown>>}
          defaultValues={{
            ...initialValues,
            image: initialValues?.image || getRandomImageUrl(),
            detail: initialValues?.detail || '',
            userManual: initialValues?.userManual || '',
          }}
          className="space-y-6"
        >
          <FormItem name="name" label={t('marketplace:product.form.name', 'Tên sản phẩm')} required>
            <Input
              placeholder={t('marketplace:product.form.namePlaceholder', 'Nhập tên sản phẩm')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('marketplace:product.form.description', 'Mô tả sản phẩm')}
          >
            <Input
              placeholder={t('marketplace:product.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
              fullWidth
            />
          </FormItem>

          <FormItem name="image" label={t('marketplace:product.form.images', 'Ảnh sản phẩm')} required>
            <div className="space-y-6">
              {/* Upload ảnh mới */}
              <div>
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={e => {
                    const files = Array.from(e.target.files || []);
                    files.forEach(file => {
                      const reader = new FileReader();
                      reader.onload = event => {
                        if (event.target?.result) {
                          handleAddImage(file, event.target.result as string);
                        }
                      };
                      reader.readAsDataURL(file);
                    });
                  }}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed border-border rounded-lg cursor-pointer bg-card hover:bg-card-muted transition-colors duration-200"
                >
                  <Icon name="upload" size="lg" className="text-muted mb-3" />
                  <span className="text-sm text-foreground font-medium">
                    {t(
                      'marketplace:product.form.selectImages',
                      'Kéo thả hoặc click để tải lên ảnh sản phẩm'
                    )}
                  </span>
                  <span className="text-xs text-muted mt-2">
                    {t(
                      'marketplace:product.form.imagesHelp',
                      'Hỗ trợ nhiều ảnh, định dạng: JPG, PNG'
                    )}
                  </span>
                </label>
              </div>

              {/* Hiển thị ảnh đã chọn */}
              {imagePreviews.length > 0 && (
                <div className="space-y-4">
                  <Typography variant="h6" className="text-foreground">
                    {t('marketplace:productsForSale.form.selectedFiles', 'Tệp tin đã chọn')} ({imagePreviews.length})
                  </Typography>

                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                    {imagePreviews.map((preview, index) => (
                      <div key={index} className="relative group">
                        <div className="relative overflow-hidden rounded-lg border border-border bg-card">
                          <img
                            src={preview}
                            alt={`Product ${index + 1}`}
                            className="w-full h-24 object-cover"
                          />

                          {/* Overlay khi hover */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleRemoveImage(index);
                              }}
                              className="opacity-0 group-hover:opacity-100 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full w-8 h-8 flex items-center justify-center transition-all duration-200 transform scale-90 group-hover:scale-100"
                              title={t('marketplace:common.delete', 'Xóa')}
                            >
                              <Icon name="x" size="sm" />
                            </button>
                          </div>
                        </div>

                        {/* Tên file (nếu có) */}
                        <div className="mt-1 text-xs text-muted truncate">
                          {imageFiles[index - (initialValues?.images?.length || 0)]?.name || `Ảnh ${index + 1}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Thông tin file mới được thêm */}
              {imageFiles.length > 0 && (
                <div className="text-sm text-muted bg-card-muted p-3 rounded-lg border border-border">
                  <p className="font-medium">
                    {t('marketplace:productsForSale.form.selectedImages', 'Đã chọn {{count}} ảnh mới', {
                      count: imageFiles.length,
                    })}
                  </p>
                </div>
              )}
            </div>
          </FormItem>

          <FormItem name="price" label={t('marketplace:product.form.price', 'Giá bán (rpoint)')}>
            <Input
              type="number"
              placeholder={t(
                'marketplace:product.form.pricePlaceholder',
                'Nhập giá bán (để trống nếu không thay đổi)'
              )}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem name="category" label={t('marketplace:product.form.category', 'Thể loại')} required>
            <Select
              placeholder={t('marketplace:product.form.categoryPlaceholder', 'Chọn thể loại')}
              options={[
                {
                  value: ProductCategory.AGENT,
                  label: t('marketplace:productsForSale.categories.agent', 'AI Agent'),
                },
                {
                  value: ProductCategory.KNOWLEDGE_FILE,
                  label: t('marketplace:productsForSale.categories.knowledgeFile', 'Knowledge File'),
                },
                {
                  value: ProductCategory.FUNCTION,
                  label: t('marketplace:productsForSale.categories.function', 'Function'),
                },
                {
                  value: ProductCategory.FINETUNE,
                  label: t('marketplace:productsForSale.categories.finetune', 'Fine-tuned Model'),
                },
                {
                  value: ProductCategory.STRATEGY,
                  label: t('marketplace:productsForSale.categories.strategy', 'Strategy'),
                },
              ]}
              fullWidth
            />
          </FormItem>

          {/* Thêm input cho detail và userManual - hiển thị cho cả tạo mới và edit */}
          <Typography variant="h6">
            {initialValues?.name
              ? t('marketplace:product.form.additionalInfo', 'Thông tin bổ sung')
              : t('marketplace:product.form.productContent', 'Nội dung sản phẩm')}
          </Typography>

          <FormTextarea
            name="detail"
            label={t('marketplace:product.form.detail', 'Thông tin chi tiết sản phẩm')}
            placeholder={
              initialValues?.name
                ? t(
                    'marketplace:product.form.detailPlaceholderEdit',
                    'Nhập thông tin chi tiết sản phẩm (nếu có thay đổi)'
                  )
                : t(
                    'marketplace:product.form.detailPlaceholderCreate',
                    'Nhập thông tin chi tiết sản phẩm'
                  )
            }
            originalValue={originalDetail}
            onChangeDetected={setHasDetailChanged}
          />

          <FormTextarea
            name="userManual"
            label={t('marketplace:product.form.userManual', 'Hướng dẫn sử dụng')}
            placeholder={
              initialValues?.name
                ? t(
                    'marketplace:product.form.userManualPlaceholderEdit',
                    'Nhập hướng dẫn sử dụng sản phẩm (nếu có thay đổi)'
                  )
                : t(
                    'marketplace:product.form.userManualPlaceholderCreate',
                    'Nhập hướng dẫn sử dụng sản phẩm'
                  )
            }
            originalValue={originalUserManual}
            onChangeDetected={setHasUserManualChanged}
          />

          <div className="flex justify-end space-x-4 mt-8">
            <Button variant="outline" onClick={onCancel} >
              {t('marketplace:common.cancel', 'Hủy')}
            </Button>
            <Button variant="outline" type="submit" disabled={isSubmitting}>
              {isSubmitting ? t('marketplace:common.submitting', 'Đang lưu...') : t('marketplace:common.save', 'Lưu nháp')}
            </Button>
            {initialValues?.name && (
              <Button
                variant="primary"
                type="button"
                disabled={isSubmitting}
                onClick={() => {
                  // Get form values and submit for approval
                  const form = document.querySelector('form');
                  if (form) {
                    const formData = new FormData(form);
                    const values = Object.fromEntries(
                      formData.entries()
                    ) as unknown as ProductForSaleFormValues;
                    handleSubmitForApproval(values);
                  }
                }}
              >
                {isSubmitting
                  ? t('marketplace:common.submitting', 'Đang gửi...')
                  : t('marketplace:common.submitForApproval', 'Gửi duyệt')}
              </Button>
            )}
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default ProductForSaleForm;
